"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _reactIs = require("react-is");
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _useTimeout = _interopRequireDefault(require("@mui/utils/useTimeout"));
var _clamp = _interopRequireDefault(require("@mui/utils/clamp"));
var _zeroStyled = require("../zero-styled");
var _memoTheme = _interopRequireDefault(require("../utils/memoTheme"));
var _DefaultPropsProvider = require("../DefaultPropsProvider");
var _Zoom = _interopRequireDefault(require("../Zoom"));
var _Fab = _interopRequireDefault(require("../Fab"));
var _capitalize = _interopRequireDefault(require("../utils/capitalize"));
var _isMuiElement = _interopRequireDefault(require("../utils/isMuiElement"));
var _useForkRef = _interopRequireDefault(require("../utils/useForkRef"));
var _useControlled = _interopRequireDefault(require("../utils/useControlled"));
var _speedDialClasses = _interopRequireWildcard(require("./speedDialClasses"));
var _useSlot = _interopRequireDefault(require("../utils/useSlot"));
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    classes,
    open,
    direction
  } = ownerState;
  const slots = {
    root: ['root', `direction${(0, _capitalize.default)(direction)}`],
    fab: ['fab'],
    actions: ['actions', !open && 'actionsClosed']
  };
  return (0, _composeClasses.default)(slots, _speedDialClasses.getSpeedDialUtilityClass, classes);
};
function getOrientation(direction) {
  if (direction === 'up' || direction === 'down') {
    return 'vertical';
  }
  if (direction === 'right' || direction === 'left') {
    return 'horizontal';
  }
  return undefined;
}
const dialRadius = 32;
const spacingActions = 16;
const SpeedDialRoot = (0, _zeroStyled.styled)('div', {
  name: 'MuiSpeedDial',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.root, styles[`direction${(0, _capitalize.default)(ownerState.direction)}`]];
  }
})((0, _memoTheme.default)(({
  theme
}) => ({
  zIndex: (theme.vars || theme).zIndex.speedDial,
  display: 'flex',
  alignItems: 'center',
  pointerEvents: 'none',
  variants: [{
    props: {
      direction: 'up'
    },
    style: {
      flexDirection: 'column-reverse',
      [`& .${_speedDialClasses.default.actions}`]: {
        flexDirection: 'column-reverse',
        marginBottom: -dialRadius,
        paddingBottom: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: 'down'
    },
    style: {
      flexDirection: 'column',
      [`& .${_speedDialClasses.default.actions}`]: {
        flexDirection: 'column',
        marginTop: -dialRadius,
        paddingTop: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: 'left'
    },
    style: {
      flexDirection: 'row-reverse',
      [`& .${_speedDialClasses.default.actions}`]: {
        flexDirection: 'row-reverse',
        marginRight: -dialRadius,
        paddingRight: spacingActions + dialRadius
      }
    }
  }, {
    props: {
      direction: 'right'
    },
    style: {
      flexDirection: 'row',
      [`& .${_speedDialClasses.default.actions}`]: {
        flexDirection: 'row',
        marginLeft: -dialRadius,
        paddingLeft: spacingActions + dialRadius
      }
    }
  }]
})));
const SpeedDialFab = (0, _zeroStyled.styled)(_Fab.default, {
  name: 'MuiSpeedDial',
  slot: 'Fab'
})({
  pointerEvents: 'auto'
});
const SpeedDialActions = (0, _zeroStyled.styled)('div', {
  name: 'MuiSpeedDial',
  slot: 'Actions',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.actions, !ownerState.open && styles.actionsClosed];
  }
})({
  display: 'flex',
  pointerEvents: 'auto',
  variants: [{
    props: ({
      ownerState
    }) => !ownerState.open,
    style: {
      transition: 'top 0s linear 0.2s',
      pointerEvents: 'none'
    }
  }]
});
const SpeedDial = /*#__PURE__*/React.forwardRef(function SpeedDial(inProps, ref) {
  const props = (0, _DefaultPropsProvider.useDefaultProps)({
    props: inProps,
    name: 'MuiSpeedDial'
  });
  const theme = (0, _zeroStyled.useTheme)();
  const defaultTransitionDuration = {
    enter: theme.transitions.duration.enteringScreen,
    exit: theme.transitions.duration.leavingScreen
  };
  const {
    ariaLabel,
    FabProps: {
      ref: origDialButtonRef,
      ...FabProps
    } = {},
    children: childrenProp,
    className,
    direction = 'up',
    hidden = false,
    icon,
    onBlur,
    onClose,
    onFocus,
    onKeyDown,
    onMouseEnter,
    onMouseLeave,
    onOpen,
    open: openProp,
    openIcon,
    slots = {},
    slotProps = {},
    TransitionComponent: TransitionComponentProp,
    TransitionProps: TransitionPropsProp,
    transitionDuration = defaultTransitionDuration,
    ...other
  } = props;
  const [open, setOpenState] = (0, _useControlled.default)({
    controlled: openProp,
    default: false,
    name: 'SpeedDial',
    state: 'open'
  });
  const ownerState = {
    ...props,
    open,
    direction
  };
  const classes = useUtilityClasses(ownerState);
  const eventTimer = (0, _useTimeout.default)();

  /**
   * an index in actions.current
   */
  const focusedAction = React.useRef(0);

  /**
   * pressing this key while the focus is on a child SpeedDialAction focuses
   * the next SpeedDialAction.
   * It is equal to the first arrow key pressed while focus is on the SpeedDial
   * that is not orthogonal to the direction.
   * @type {utils.ArrowKey?}
   */
  const nextItemArrowKey = React.useRef();

  /**
   * refs to the Button that have an action associated to them in this SpeedDial
   * [Fab, ...(SpeedDialActions > Button)]
   * @type {HTMLButtonElement[]}
   */
  const actions = React.useRef([]);
  actions.current = [actions.current[0]];
  const handleOwnFabRef = React.useCallback(fabFef => {
    actions.current[0] = fabFef;
  }, []);
  const handleFabRef = (0, _useForkRef.default)(origDialButtonRef, handleOwnFabRef);

  /**
   * creates a ref callback for the Button in a SpeedDialAction
   * Is called before the original ref callback for Button that was set in buttonProps
   *
   * @param dialActionIndex {number}
   * @param origButtonRef {React.RefObject?}
   */
  const createHandleSpeedDialActionButtonRef = (dialActionIndex, origButtonRef) => {
    return buttonRef => {
      actions.current[dialActionIndex + 1] = buttonRef;
      if (origButtonRef) {
        origButtonRef(buttonRef);
      }
    };
  };
  const handleKeyDown = event => {
    if (onKeyDown) {
      onKeyDown(event);
    }
    const key = event.key.replace('Arrow', '').toLowerCase();
    const {
      current: nextItemArrowKeyCurrent = key
    } = nextItemArrowKey;
    if (event.key === 'Escape') {
      setOpenState(false);
      actions.current[0].focus();
      if (onClose) {
        onClose(event, 'escapeKeyDown');
      }
      return;
    }
    if (getOrientation(key) === getOrientation(nextItemArrowKeyCurrent) && getOrientation(key) !== undefined) {
      event.preventDefault();
      const actionStep = key === nextItemArrowKeyCurrent ? 1 : -1;

      // stay within array indices
      const nextAction = (0, _clamp.default)(focusedAction.current + actionStep, 0, actions.current.length - 1);
      actions.current[nextAction].focus();
      focusedAction.current = nextAction;
      nextItemArrowKey.current = nextItemArrowKeyCurrent;
    }
  };
  React.useEffect(() => {
    // actions were closed while navigation state was not reset
    if (!open) {
      focusedAction.current = 0;
      nextItemArrowKey.current = undefined;
    }
  }, [open]);
  const handleClose = event => {
    if (event.type === 'mouseleave' && onMouseLeave) {
      onMouseLeave(event);
    }
    if (event.type === 'blur' && onBlur) {
      onBlur(event);
    }
    eventTimer.clear();
    if (event.type === 'blur') {
      eventTimer.start(0, () => {
        setOpenState(false);
        if (onClose) {
          onClose(event, 'blur');
        }
      });
    } else {
      setOpenState(false);
      if (onClose) {
        onClose(event, 'mouseLeave');
      }
    }
  };
  const handleClick = event => {
    if (FabProps.onClick) {
      FabProps.onClick(event);
    }
    eventTimer.clear();
    if (open) {
      setOpenState(false);
      if (onClose) {
        onClose(event, 'toggle');
      }
    } else {
      setOpenState(true);
      if (onOpen) {
        onOpen(event, 'toggle');
      }
    }
  };
  const handleOpen = event => {
    if (event.type === 'mouseenter' && onMouseEnter) {
      onMouseEnter(event);
    }
    if (event.type === 'focus' && onFocus) {
      onFocus(event);
    }

    // When moving the focus between two items,
    // a chain if blur and focus event is triggered.
    // We only handle the last event.
    eventTimer.clear();
    if (!open) {
      // Wait for a future focus or click event
      eventTimer.start(0, () => {
        setOpenState(true);
        if (onOpen) {
          const eventMap = {
            focus: 'focus',
            mouseenter: 'mouseEnter'
          };
          onOpen(event, eventMap[event.type]);
        }
      });
    }
  };

  // Filter the label for valid id characters.
  const id = ariaLabel.replace(/^[^a-z]+|[^\w:.-]+/gi, '');
  const allItems = React.Children.toArray(childrenProp).filter(child => {
    if (process.env.NODE_ENV !== 'production') {
      if ((0, _reactIs.isFragment)(child)) {
        console.error(["MUI: The SpeedDial component doesn't accept a Fragment as a child.", 'Consider providing an array instead.'].join('\n'));
      }
    }
    return /*#__PURE__*/React.isValidElement(child);
  });
  const children = allItems.map((child, index) => {
    const {
      FabProps: {
        ref: origButtonRef,
        ...ChildFabProps
      } = {},
      tooltipPlacement: tooltipPlacementProp
    } = child.props;
    const tooltipPlacement = tooltipPlacementProp || (getOrientation(direction) === 'vertical' ? 'left' : 'top');
    return /*#__PURE__*/React.cloneElement(child, {
      FabProps: {
        ...ChildFabProps,
        ref: createHandleSpeedDialActionButtonRef(index, origButtonRef)
      },
      delay: 30 * (open ? index : allItems.length - index),
      open,
      tooltipPlacement,
      id: `${id}-action-${index}`
    });
  });
  const backwardCompatibleSlots = {
    transition: TransitionComponentProp,
    ...slots
  };
  const backwardCompatibleSlotProps = {
    transition: TransitionPropsProp,
    ...slotProps
  };
  const externalForwardedProps = {
    slots: backwardCompatibleSlots,
    slotProps: backwardCompatibleSlotProps
  };
  const [RootSlot, rootSlotProps] = (0, _useSlot.default)('root', {
    elementType: SpeedDialRoot,
    externalForwardedProps: {
      ...externalForwardedProps,
      ...other
    },
    ownerState,
    ref,
    className: (0, _clsx.default)(classes.root, className),
    additionalProps: {
      role: 'presentation'
    },
    getSlotProps: handlers => ({
      ...handlers,
      onKeyDown: event => {
        handlers.onKeyDown?.(event);
        handleKeyDown(event);
      },
      onBlur: event => {
        handlers.onBlur?.(event);
        handleClose(event);
      },
      onFocus: event => {
        handlers.onFocus?.(event);
        handleOpen(event);
      },
      onMouseEnter: event => {
        handlers.onMouseEnter?.(event);
        handleOpen(event);
      },
      onMouseLeave: event => {
        handlers.onMouseLeave?.(event);
        handleClose(event);
      }
    })
  });
  const [TransitionSlot, transitionProps] = (0, _useSlot.default)('transition', {
    elementType: _Zoom.default,
    externalForwardedProps,
    ownerState
  });
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(RootSlot, {
    ...rootSlotProps,
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(TransitionSlot, {
      in: !hidden,
      timeout: transitionDuration,
      unmountOnExit: true,
      ...transitionProps,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(SpeedDialFab, {
        color: "primary",
        "aria-label": ariaLabel,
        "aria-haspopup": "true",
        "aria-expanded": open,
        "aria-controls": `${id}-actions`,
        ...FabProps,
        onClick: handleClick,
        className: (0, _clsx.default)(classes.fab, FabProps.className),
        ref: handleFabRef,
        ownerState: ownerState,
        children: /*#__PURE__*/React.isValidElement(icon) && (0, _isMuiElement.default)(icon, ['SpeedDialIcon']) ? /*#__PURE__*/React.cloneElement(icon, {
          open
        }) : icon
      })
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(SpeedDialActions, {
      id: `${id}-actions`,
      role: "menu",
      "aria-orientation": getOrientation(direction),
      className: (0, _clsx.default)(classes.actions, !open && classes.actionsClosed),
      ownerState: ownerState,
      children: children
    })]
  });
});
process.env.NODE_ENV !== "production" ? SpeedDial.propTypes /* remove-proptypes */ = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The aria-label of the button element.
   * Also used to provide the `id` for the `SpeedDial` element and its children.
   */
  ariaLabel: _propTypes.default.string.isRequired,
  /**
   * SpeedDialActions to display when the SpeedDial is `open`.
   */
  children: _propTypes.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  /**
   * @ignore
   */
  className: _propTypes.default.string,
  /**
   * The direction the actions open relative to the floating action button.
   * @default 'up'
   */
  direction: _propTypes.default.oneOf(['down', 'left', 'right', 'up']),
  /**
   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) element.
   * @default {}
   */
  FabProps: _propTypes.default.object,
  /**
   * If `true`, the SpeedDial is hidden.
   * @default false
   */
  hidden: _propTypes.default.bool,
  /**
   * The icon to display in the SpeedDial Fab. The `SpeedDialIcon` component
   * provides a default Icon with animation.
   */
  icon: _propTypes.default.node,
  /**
   * @ignore
   */
  onBlur: _propTypes.default.func,
  /**
   * Callback fired when the component requests to be closed.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"blur"`, `"mouseLeave"`, `"escapeKeyDown"`.
   */
  onClose: _propTypes.default.func,
  /**
   * @ignore
   */
  onFocus: _propTypes.default.func,
  /**
   * @ignore
   */
  onKeyDown: _propTypes.default.func,
  /**
   * @ignore
   */
  onMouseEnter: _propTypes.default.func,
  /**
   * @ignore
   */
  onMouseLeave: _propTypes.default.func,
  /**
   * Callback fired when the component requests to be open.
   *
   * @param {object} event The event source of the callback.
   * @param {string} reason Can be: `"toggle"`, `"focus"`, `"mouseEnter"`.
   */
  onOpen: _propTypes.default.func,
  /**
   * If `true`, the component is shown.
   */
  open: _propTypes.default.bool,
  /**
   * The icon to display in the SpeedDial Fab when the SpeedDial is open.
   */
  openIcon: _propTypes.default.node,
  /**
   * The props used for each slot inside.
   * @default {}
   */
  slotProps: _propTypes.default.shape({
    root: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object]),
    transition: _propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object])
  }),
  /**
   * The components used for each slot inside.
   * @default {}
   */
  slots: _propTypes.default.shape({
    root: _propTypes.default.elementType,
    transition: _propTypes.default.elementType
  }),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),
  /**
   * The component used for the transition.
   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.
   * @default Zoom
   * * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionComponent: _propTypes.default.elementType,
  /**
   * The duration for the transition, in milliseconds.
   * You may specify a single timeout for all transitions, or individually with an object.
   * @default {
   *   enter: theme.transitions.duration.enteringScreen,
   *   exit: theme.transitions.duration.leavingScreen,
   * }
   */
  transitionDuration: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.shape({
    appear: _propTypes.default.number,
    enter: _propTypes.default.number,
    exit: _propTypes.default.number
  })]),
  /**
   * Props applied to the transition element.
   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.
   * @deprecated Use `slotProps.transition` instead. This prop will be removed in a future major release. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
   */
  TransitionProps: _propTypes.default.object
} : void 0;
var _default = exports.default = SpeedDial;